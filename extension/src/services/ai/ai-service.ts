import type { 
  Project, 
  OptimizationContext, 
  AIOptimizationResult
} from '~/types'
import type { AIService as IAIService } from '../interfaces'
import { StorageFactory } from '../storage'
import { getAppConfig } from '~/lib/config'

interface AIConfig {
  apiUrl: string
  apiKey?: string
  timeout: number
}

export class AIService implements IAIService {
  private config: AIConfig | null = null
  private storageFactory: StorageFactory
  private optimizationId: string | null = null

  constructor() {
    this.storageFactory = StorageFactory.getInstance()
    this.initializeConfig()
  }

  private async initializeConfig(): Promise<void> {
    try {
      const appConfig = getAppConfig()
      
      // 使用环境配置，默认为免费用户配置
      this.config = {
        apiUrl: `${appConfig.apiUrl}/extension`,
        timeout: appConfig.timeout
      }

      // 为免费用户生成唯一ID
      this.optimizationId = this.generateOptimizationId()
    } catch (error) {
      console.error('AI服务初始化失败:', error)
      const appConfig = getAppConfig()
      this.config = {
        apiUrl: `${appConfig.apiUrl}/extension`,
        timeout: appConfig.timeout
      }
      this.optimizationId = this.generateOptimizationId()
    }
  }

  async optimizeContent(content: string, context: OptimizationContext): Promise<string> {
    if (!this.isAvailable()) {
      throw new Error('AI服务不可用')
    }

    if (!content || content.trim().length === 0) {
      throw new Error('内容不能为空')
    }

    try {
      const optimized = await this.callOptimizeAPI(content, context)
      console.log('内容优化成功')
      return optimized
    } catch (error) {
      console.error('内容优化失败:', error)
      
      // 降级处理：返回原始内容
      console.warn('使用原始内容作为降级方案')
      return content
    }
  }

  async generateDescription(project: Project, targetPlatform?: string): Promise<string> {
    const context: OptimizationContext = {
      targetPlatform,
      fieldType: 'description',
      maxLength: 500,
      tone: 'professional'
    }

    // 基于项目信息构建提示内容
    const baseContent = this.buildProjectDescription(project)
    
    try {
      return await this.optimizeContent(baseContent, context)
    } catch (error) {
      console.error('生成项目描述失败:', error)
      return baseContent
    }
  }

  async suggestTags(project: Project): Promise<string[]> {
    try {
      const context: OptimizationContext = {
        fieldType: 'tags',
        tone: 'professional'
      }

      const content = `项目名称: ${project.name}\n项目介绍: ${project.info?.introduction || project.name}\n项目域名: ${project.domain}`
      const response = await this.callOptimizeAPI(content, { ...context, maxLength: 200 })
      
      // 解析标签字符串
      const suggestedTags = response
        .split(/[,，\n]/)
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0 && tag.length < 50)
        .slice(0, 10) // 最多10个标签

      return suggestedTags
    } catch (error) {
      console.error('生成标签建议失败:', error)
      
      // 降级方案：基于项目信息提取关键词
      return this.extractKeywordsFromProject(project)
    }
  }

  isAvailable(): boolean {
    return this.config !== null && navigator.onLine
  }

  generateOptimizationId(): string {
    // 为免费用户生成唯一ID
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    const browserFingerprint = this.getBrowserFingerprint()
    
    return `free_${timestamp}_${random}_${browserFingerprint}`
  }

  // 高级AI功能
  async optimizeForPlatform(project: Project, platform: string): Promise<{
    title: string
    description: string
    tags: string[]
    customFields: Record<string, string>
  }> {
    try {
      const optimizations = await Promise.all([
        this.optimizeContent(project.name, {
          targetPlatform: platform,
          fieldType: 'title',
          maxLength: 100,
          tone: 'marketing'
        }),
        this.optimizeContent(project.info?.introduction || project.name, {
          targetPlatform: platform,
          fieldType: 'description',
          maxLength: 500,
          tone: 'professional'
        }),
        this.suggestTags(project)
      ])

      return {
        title: optimizations[0],
        description: optimizations[1],
        tags: optimizations[2],
        customFields: await this.generateCustomFields(project, platform)
      }
    } catch (error) {
      console.error('平台优化失败:', error)
      
      // 降级方案
      return {
        title: project.name,
        description: project.info?.introduction || project.name,
        tags: [],
        customFields: {}
      }
    }
  }

  async batchOptimize(contents: string[], context: OptimizationContext): Promise<string[]> {
    const results: string[] = []
    
    // 批量处理，但限制并发数
    const BATCH_SIZE = 3
    
    for (let i = 0; i < contents.length; i += BATCH_SIZE) {
      const batch = contents.slice(i, i + BATCH_SIZE)
      
      try {
        const batchResults = await Promise.all(
          batch.map(content => this.optimizeContent(content, context))
        )
        results.push(...batchResults)
      } catch (error) {
        console.error(`批量优化第${Math.floor(i / BATCH_SIZE) + 1}批失败:`, error)
        // 降级：使用原始内容
        results.push(...batch)
      }
    }
    
    return results
  }

  // 私有方法
  private async callOptimizeAPI(content: string, context: OptimizationContext): Promise<string> {
    if (!this.config) {
      throw new Error('AI服务配置未初始化')
    }

    const url = `${this.config.apiUrl}/optimize`

    const payload: any = {
      content,
      context,
      timestamp: Date.now()
    }

    // 免费用户添加唯一ID
    if (!this.config.apiKey && this.optimizationId) {
      payload.optimizationId = this.optimizationId
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    // 付费用户添加认证头
    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`AI API请求失败 (${response.status}): ${errorText}`)
      }

      const result = await response.json()
      return result.optimized || result.content || content
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('AI服务请求超时')
      }
      
      throw error
    }
  }

  private buildProjectDescription(project: Project): string {
    let description = `项目名称: ${project.name}\n项目域名: ${project.domain}`

    if (project.info?.introduction) {
      description += `\n项目介绍: ${project.info.introduction}`
    }

    if (project.category) {
      description += `\n项目类型: ${project.category}`
    }

    return description
  }

  private extractKeywordsFromProject(project: Project): string[] {
    const keywords = new Set<string>()

    // 从项目名称提取
    const nameWords = project.name.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
    
    nameWords.forEach(word => keywords.add(word))

    // 从介绍提取常见技术词汇
    const techKeywords = [
      'react', 'vue', 'angular', 'node', 'python', 'java', 'javascript', 'typescript',
      'mobile', 'web', 'app', 'api', 'database', 'ai', 'ml', 'blockchain', 'iot'
    ]

    const introductionLower = (project.info?.introduction || project.name).toLowerCase()
    techKeywords.forEach(keyword => {
      if (introductionLower.includes(keyword)) {
        keywords.add(keyword)
      }
    })

    // 从分类添加关键词
    if (project.category) {
      keywords.add(project.category.toLowerCase())
    }

    return Array.from(keywords).slice(0, 8)
  }

  private async generateCustomFields(project: Project, platform: string): Promise<Record<string, string>> {
    // 根据平台生成自定义字段
    const customFields: Record<string, string> = {}

    try {
      // 这里可以根据不同平台的要求生成特定字段
      switch (platform.toLowerCase()) {
        case 'producthunt':
          customFields.hunter_comment = await this.optimizeContent(
            `为什么推荐 ${project.name}`,
            { fieldType: 'description', maxLength: 200, tone: 'marketing' }
          )
          break
        
        case 'hacker_news':
          customFields.hn_title = await this.optimizeContent(
            project.name,
            { fieldType: 'title', maxLength: 80, tone: 'casual' }
          )
          break
        
        case 'reddit':
          customFields.reddit_post = await this.optimizeContent(
            project.info?.introduction || project.name,
            { fieldType: 'description', maxLength: 300, tone: 'casual' }
          )
          break
      }
    } catch (error) {
      console.error('生成自定义字段失败:', error)
    }

    return customFields
  }

  private getBrowserFingerprint(): string {
    // 生成简单的浏览器指纹
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.textBaseline = 'top'
      ctx.font = '14px Arial'
      ctx.fillText('LinkTrackPro', 2, 2)
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // 生成简短的哈希
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }
}