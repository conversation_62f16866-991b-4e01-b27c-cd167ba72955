import type { 
  Project, 
  CreateProjectDto, 
  ExternalLink, 
  CreateLinkDto, 
  UserSettings,
  StorageService 
} from '~/types'

// 存储服务抽象基类
export abstract class BaseStorageService implements StorageService {
  protected abstract storageType: 'local' | 'api'

  // 项目操作
  abstract getProjects(): Promise<Project[]>
  abstract addProject(project: CreateProjectDto): Promise<Project>
  abstract updateProject(id: string, updates: Partial<Project>): Promise<Project>
  abstract deleteProject(id: string): Promise<void>

  // 外链操作
  abstract getLinks(): Promise<ExternalLink[]>
  abstract addLink(link: CreateLinkDto): Promise<ExternalLink>
  abstract updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink>
  abstract deleteLink(id: string): Promise<void>

  // 设置操作
  abstract getSettings(): Promise<UserSettings>
  abstract updateSettings(updates: Partial<UserSettings>): Promise<UserSettings>

  // 同步操作
  abstract syncWithAPI(): Promise<void>
  abstract isOnline(): boolean

  // 通用工具方法
  protected generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  protected createTimestamp(): Date {
    return new Date()
  }

  protected validateProject(project: CreateProjectDto): void {
    if (!project.name || project.name.trim().length === 0) {
      throw new Error('项目名称不能为空')
    }
    if (!project.domain || project.domain.trim().length === 0) {
      throw new Error('项目域名不能为空')
    }
  }

  protected validateLink(link: CreateLinkDto): void {
    if (!link.name || link.name.trim().length === 0) {
      throw new Error('外链平台名称不能为空')
    }
    if (!link.url || !this.isValidUrl(link.url)) {
      throw new Error('外链平台URL格式不正确')
    }
    if (link.submitUrl && !this.isValidUrl(link.submitUrl)) {
      throw new Error('提交URL格式不正确')
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  // 数据转换工具
  protected deserializeProject(data: any): Project {
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
      screenshots: data.screenshots || [],
      info: data.info || {}
    }
  }

  protected deserializeLink(data: any): ExternalLink {
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
      requirements: data.requirements || []
    }
  }

  protected serializeProject(project: Project): any {
    return {
      ...project,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString()
    }
  }

  protected serializeLink(link: ExternalLink): any {
    return {
      ...link,
      createdAt: link.createdAt.toISOString(),
      updatedAt: link.updatedAt.toISOString()
    }
  }
}