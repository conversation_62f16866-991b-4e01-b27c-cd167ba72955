import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { AIOptimizer } from '~/components/ai/ai-optimizer'

interface FormFillerProps {
  selectedForm: any | null
}

export function FormFiller({ selectedForm }: FormFillerProps) {
  const { 
    projects, 
    selectedProject, 
    selectProject, 
    fillForm,
    optimizeContent,
    aiOptimizing,
    error 
  } = useAppStore()

  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [fillPreview, setFillPreview] = useState<any>(null)
  const [isFillingForm, setIsFillingForm] = useState(false)
  const [fillResult, setFillResult] = useState<any>(null)
  const [customMapping, setCustomMapping] = useState<any>({})
  const [aiOptimizerOpen, setAiOptimizerOpen] = useState(false)
  const [optimizingFieldIndex, setOptimizingFieldIndex] = useState<number | null>(null)
  const [optimizingContent, setOptimizingContent] = useState('')
  const [optimizingFieldType, setOptimizingFieldType] = useState<'title' | 'description' | 'tags'>('description')

  // 预览填充效果
  const handlePreviewFill = async () => {
    if (!selectedForm || !selectedProject) return

    setIsPreviewOpen(true)
    setFillPreview(null)
    
    try {
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) throw new Error('无法获取当前标签页')

      // 发送预览请求到content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        type: 'PREVIEW_FORM_FILL',
        formIndex: selectedForm.index,
        project: selectedProject
      })

      if (response && response.success) {
        setFillPreview(response.preview)
      } else {
        throw new Error(response?.error || '预览填充失败')
      }
    } catch (error) {
      console.error('预览填充失败:', error)
      
      // 如果直接通信失败，创建本地预览数据作为降级
      const fallbackPreview = {
        fieldPreviews: selectedForm.fields.map((field: any) => ({
          field,
          currentValue: '',
          proposedValue: getProposedValue(field, selectedProject),
          confidence: 0.8
        })),
        overallConfidence: 0.8
      }
      
      setFillPreview(fallbackPreview)
    }
  }

  // 根据字段类型获取建议值
  const getProposedValue = (field: any, project: any) => {
    const fieldText = `${field.name} ${field.label || ''} ${field.placeholder || ''}`.toLowerCase()
    
    if (fieldText.includes('name') || fieldText.includes('title')) {
      return project.name
    }
    if (fieldText.includes('url') || fieldText.includes('website') || fieldText.includes('link')) {
      return project.url
    }
    if (fieldText.includes('description') || fieldText.includes('desc') || fieldText.includes('about')) {
      return field.type === 'textarea' ? project.description : (project.shortDescription || project.description.substring(0, 200))
    }
    if (fieldText.includes('tag') || fieldText.includes('keyword')) {
      return project.tags.join(', ')
    }
    if (fieldText.includes('category') || fieldText.includes('type')) {
      return project.category || ''
    }
    
    return ''
  }

  // 执行表单填充
  const handleFillForm = async () => {
    if (!selectedForm || !selectedProject) return

    setIsFillingForm(true)
    setFillResult(null)

    try {
      const result = await fillForm(selectedForm.index, selectedProject, customMapping)
      setFillResult(result)
      
      if (result.success) {
        setIsPreviewOpen(false)
      }
    } catch (error) {
      // 错误已在store中处理
    } finally {
      setIsFillingForm(false)
    }
  }

  // AI优化字段内容
  const handleOptimizeField = (fieldIndex: number, content: string, field: any) => {
    if (!(content || '').trim()) return

    // 判断字段类型
    let fieldType: 'title' | 'description' | 'tags' = 'description'
    const fieldText = `${field.name} ${field.label || ''} ${field.placeholder || ''}`.toLowerCase()
    
    if (fieldText.includes('name') || fieldText.includes('title')) {
      fieldType = 'title'
    } else if (fieldText.includes('tag') || fieldText.includes('keyword')) {
      fieldType = 'tags'
    } else {
      fieldType = 'description'
    }

    setOptimizingFieldIndex(fieldIndex)
    setOptimizingContent(content)
    setOptimizingFieldType(fieldType)
    setAiOptimizerOpen(true)
  }

  // 处理AI优化完成
  const handleOptimizeComplete = (optimizedContent: string) => {
    if (optimizingFieldIndex !== null) {
      // 更新预览中的内容
      setFillPreview((prev: any) => ({
        ...prev,
        fieldPreviews: prev.fieldPreviews.map((preview: any, index: number) => 
          index === optimizingFieldIndex ? { ...preview, proposedValue: optimizedContent } : preview
        )
      }))
    }
    
    // 重置状态
    setOptimizingFieldIndex(null)
    setOptimizingContent('')
    setAiOptimizerOpen(false)
  }

  if (!selectedForm) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <div className="text-muted-foreground mb-4">
            <svg className="w-12 h-12 mx-auto text-muted-foreground/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <p className="text-muted-foreground">请先选择要填充的表单</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">表单填充</h2>
      </div>

      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
          {error}
        </div>
      )}

      {fillResult && (
        <Card className={fillResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          <CardContent className="py-4">
            <div className="flex items-center gap-2 mb-2">
              {fillResult.success ? (
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span className={`font-medium ${fillResult.success ? 'text-green-800' : 'text-red-800'}`}>
                {fillResult.success ? '填充成功' : '填充失败'}
              </span>
            </div>

            {fillResult.filledFields.length > 0 && (
              <p className="text-sm text-green-700 mb-2">
                成功填充了 {fillResult.filledFields.length} 个字段: {fillResult.filledFields.join(', ')}
              </p>
            )}

            {fillResult.errors.length > 0 && (
              <div className="text-sm text-red-700">
                <p className="font-medium mb-1">错误信息:</p>
                <ul className="list-disc list-inside space-y-1">
                  {fillResult.errors.map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {fillResult.warnings.length > 0 && (
              <div className="text-sm text-yellow-700 mt-2">
                <p className="font-medium mb-1">警告信息:</p>
                <ul className="list-disc list-inside space-y-1">
                  {fillResult.warnings.map((warning: string, index: number) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 选择项目 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">选择项目</CardTitle>
          <CardDescription>选择要用于填充表单的项目</CardDescription>
        </CardHeader>
        <CardContent>
          {projects.length === 0 ? (
            <p className="text-muted-foreground">没有可用的项目，请先添加项目</p>
          ) : (
            <div className="space-y-2">
              {projects.slice(0, 4).map((project) => (
                <div
                  key={project.id}
                  className={`p-2 rounded border cursor-pointer transition-colors ${
                    selectedProject?.id === project.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-accent'
                  }`}
                  onClick={() => selectProject(project)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">{project.name}</h4>
                        {selectedProject?.id === project.id && (
                          <svg className="w-4 h-4 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground truncate">{project.url}</p>
                    </div>
                  </div>
                </div>
              ))}
              
              {projects.length > 4 && (
                <div className="p-2 text-center border border-dashed rounded">
                  <p className="text-xs text-muted-foreground">
                    还有 {projects.length - 4} 个项目...
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 表单信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">表单信息</CardTitle>
          <CardDescription>当前选择的表单详情</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span>表单编号:</span>
              <span>#{selectedForm.index + 1}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <span>字段数量:</span>
              <span>{selectedForm.fieldsCount} 个</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <span>匹配度:</span>
              <span className={`px-2 py-1 rounded text-xs ${
                selectedForm.confidence >= 0.8 ? 'bg-green-100 text-green-800' :
                selectedForm.confidence >= 0.6 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {(selectedForm.confidence * 100).toFixed(0)}%
              </span>
            </div>

            {selectedForm.context?.title && (
              <div className="text-sm">
                <span className="text-muted-foreground">表单标题:</span>
                <div className="mt-1 p-2 bg-muted rounded text-sm">
                  {selectedForm.context.title}
                </div>
              </div>
            )}

            <div className="text-sm">
              <span className="text-muted-foreground">检测到的字段:</span>
              <div className="mt-2 flex flex-wrap gap-1">
                {selectedForm.fields.map((field: any, index: number) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded text-xs bg-secondary text-secondary-foreground"
                  >
                    {field.label || field.name || field.placeholder || field.type}
                    {field.required && <span className="ml-1 text-red-500">*</span>}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex gap-3">
        <Button
          onClick={handlePreviewFill}
          disabled={!selectedProject}
          variant="outline"
          className="flex-1"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          预览填充
        </Button>
        
        <Button
          onClick={handleFillForm}
          disabled={!selectedProject || isFillingForm}
          className="flex-1"
        >
          {isFillingForm ? (
            <>
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              填充中...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              立即填充
            </>
          )}
        </Button>
      </div>

      {/* 填充预览对话框 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>填充预览</DialogTitle>
            <DialogDescription>
              预览将要填充到表单中的内容，您可以进行调整
            </DialogDescription>
          </DialogHeader>

          {fillPreview && (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted rounded">
                <div>
                  <span className="font-medium">整体匹配度</span>
                  <span className="ml-2 text-sm text-muted-foreground">
                    {(fillPreview.overallConfidence * 100).toFixed(0)}%
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {fillPreview.fieldPreviews.filter((p: any) => p.proposedValue).length} / {fillPreview.fieldPreviews.length} 个字段将被填充
                </span>
              </div>

              <div className="space-y-3">
                {fillPreview.fieldPreviews.map((preview: any, index: number) => (
                  <Card key={index} className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">
                          {preview.field.label || preview.field.name || `字段 ${index + 1}`}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          类型: {preview.field.type} 
                          {preview.field.required && <span className="text-red-500 ml-1">*</span>}
                        </p>
                      </div>
                      
                      {preview.proposedValue && (
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleOptimizeField(index, preview.proposedValue, preview.field)}
                            disabled={aiOptimizing || !preview.proposedValue}
                            title="AI优化内容"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </Button>
                        </div>
                      )}
                    </div>

                    {preview.currentValue && (
                      <div className="mb-2">
                        <label className="text-sm text-muted-foreground">当前值:</label>
                        <div className="mt-1 p-2 bg-gray-50 rounded text-sm">
                          {preview.currentValue}
                        </div>
                      </div>
                    )}

                    <div>
                      <label className="text-sm text-muted-foreground">将填充的值:</label>
                      <div className="mt-1">
                        {preview.proposedValue ? (
                          <textarea
                            className="w-full p-2 border rounded text-sm min-h-[60px] resize-vertical"
                            value={preview.proposedValue}
                            onChange={(e) => {
                              setFillPreview((prev: any) => ({
                                ...prev,
                                fieldPreviews: prev.fieldPreviews.map((p: any, i: number) => 
                                  i === index ? { ...p, proposedValue: e.target.value } : p
                                )
                              }))
                            }}
                          />
                        ) : (
                          <div className="p-2 bg-gray-50 rounded text-sm text-muted-foreground italic">
                            无法自动匹配，将跳过此字段
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleFillForm} disabled={isFillingForm}>
                  {isFillingForm ? '填充中...' : '确认填充'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* AI优化器对话框 */}
      <AIOptimizer
        isOpen={aiOptimizerOpen}
        onClose={() => setAiOptimizerOpen(false)}
        initialContent={optimizingContent}
        contentType={optimizingFieldType}
        onOptimized={handleOptimizeComplete}
      />
    </div>
  )
}