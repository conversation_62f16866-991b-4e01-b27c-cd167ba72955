// 项目数据模型
export interface Project {
  id: string
  name: string
  domain: string
  info?: {
    introduction?: string
  }
  screenshots: string[]
  category?: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateProjectDto {
  name: string
  domain: string
  info?: {
    introduction?: string
  }
  screenshots?: string[]  
  category?: string
}

// 外链数据模型
export interface ExternalLink {
  id: string
  name: string
  url: string
  submitUrl?: string
  isPaid: boolean
  category?: string
  requirements?: string[]
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateLinkDto {
  name: string
  url: string
  submitUrl?: string
  isPaid?: boolean
  category?: string
  requirements?: string[]
  notes?: string
}

// 表单检测模型 - 重新设计
export interface FormElement {
  id: string                      // 唯一标识符
  element: HTMLFormElement
  fields: FormField[]
  submitButton?: HTMLButtonElement
  confidence: number
  metadata: FormMetadata
  detectionContext: DetectionContext
  lastUpdated: number             // 时间戳
}

export interface FormField {
  id: string                      // 字段唯一标识符
  element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  type: FormFieldType
  name: string
  label?: string
  placeholder?: string
  required: boolean
  maxLength?: number
  semanticType?: SemanticFieldType // 语义类型
  confidence: number              // 字段匹配置信度
  validationRules?: ValidationRule[]
  context: FieldContext
}

export type FormFieldType = 
  | 'text' | 'email' | 'url' | 'textarea' | 'select' | 'file'
  | 'number' | 'tel' | 'password' | 'search' | 'date'
  | 'checkbox' | 'radio' | 'range' | 'color'

export type SemanticFieldType = 
  | 'project_name' | 'project_introduction' | 'project_domain'
  | 'contact_email' | 'company_name' | 'category'
  | 'demo_url' | 'social_media' | 'pricing' | 'team_size'
  | 'unknown'

export interface FormMetadata {
  title?: string
  description?: string
  category?: FormCategory
  platform?: string               // 检测到的平台类型
  framework?: WebFramework        // 检测到的Web框架
  submitUrl?: string
  method: string
  enctype?: string
  hasValidation: boolean
  isMultiStep: boolean
  estimatedCompletionTime?: number // 秒
}

export interface DetectionContext {
  detectorType: DetectorType
  detectionStrategy: DetectionStrategy
  pageContext: PageContext
  performanceMetrics: PerformanceMetrics
}

export interface FieldContext {
  position: number                // 在表单中的位置
  groupName?: string             // 字段组名
  dependencies?: string[]        // 依赖的其他字段
  conditionalLogic?: ConditionalRule[]
  visualProperties: VisualProperties
}

export interface VisualProperties {
  visible: boolean
  position: DOMRect
  zIndex: number
  backgroundColor?: string
  fontSize?: number
}

export interface ValidationRule {
  type: 'required' | 'pattern' | 'length' | 'custom'
  value?: any
  message?: string
}

export interface ConditionalRule {
  dependsOn: string
  condition: 'equals' | 'not_equals' | 'contains' | 'empty'
  value: any
}

export type FormCategory = 
  | 'submission' | 'contact' | 'signup' | 'login'
  | 'survey' | 'order' | 'support' | 'newsletter'
  | 'application' | 'unknown'

export type WebFramework = 
  | 'react' | 'vue' | 'angular' | 'svelte'
  | 'jquery' | 'vanilla' | 'unknown'

export type DetectorType = 
  | 'dom' | 'semantic' | 'ai' | 'behavioral' | 'hybrid'

export type DetectionStrategy = 
  | 'deep_scan' | 'quick_scan' | 'incremental' | 'targeted'

export interface PageContext {
  url: string
  title: string
  domain: string
  language?: string
  contentType: string
  hasJavaScript: boolean
  loadTime: number
  domComplexity: number
}

export interface PerformanceMetrics {
  detectionTime: number          // 检测耗时(ms)
  elementsScanned: number        // 扫描的元素数量
  memoryUsage?: number           // 内存使用(bytes)
  cacheHitRate?: number          // 缓存命中率
}

export interface FieldMapping {
  [fieldName: string]: {
    projectField: keyof Project
    transform?: (value: any) => string
  }
}

export interface FillResult {
  success: boolean
  filledFields: string[]
  errors: string[]
  warnings: string[]
}

// AI优化模型
export interface OptimizationContext {
  targetPlatform?: string
  fieldType: 'title' | 'description' | 'tags'
  maxLength?: number
  tone?: 'professional' | 'casual' | 'marketing'
}

export interface AIOptimizationResult {
  original: string
  optimized: string
  confidence: number
  suggestions?: string[]
}

// 错误处理
export enum ErrorType {
  STORAGE_ERROR = 'STORAGE_ERROR',
  API_ERROR = 'API_ERROR',
  FORM_DETECTION_ERROR = 'FORM_DETECTION_ERROR',
  FORM_FILL_ERROR = 'FORM_FILL_ERROR',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export interface AppError {
  type: ErrorType
  message: string
  details?: any
  timestamp: Date
}

// 存储服务接口
export interface StorageService {
  getProjects(): Promise<Project[]>
  addProject(project: CreateProjectDto): Promise<Project>
  updateProject(id: string, updates: Partial<Project>): Promise<Project>
  deleteProject(id: string): Promise<void>
  
  getLinks(): Promise<ExternalLink[]>
  addLink(link: CreateLinkDto): Promise<ExternalLink>
  updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink>
  deleteLink(id: string): Promise<void>
  
  syncWithAPI(): Promise<void>
}

// 项目管理器接口
export interface ProjectManager {
  getProjects(): Promise<Project[]>
  addProject(project: CreateProjectDto): Promise<Project>
  updateProject(id: string, updates: Partial<Project>): Promise<Project>
  deleteProject(id: string): Promise<void>
  syncWithAPI(): Promise<void>
  searchProjects(query: string): Promise<Project[]>
  getProjectsByCategory(category: string): Promise<Project[]>
  getProjectStats(): Promise<{
    total: number
    byCategory: Record<string, number>
    recentlyUpdated: Project[]
  }>
}

// 表单检测器接口
export interface BaseFormDetector {
  readonly name: string
  readonly priority: number
  readonly strategy: DetectionStrategy
  
  detectForms(context: DetectionEnvironment): Promise<FormElement[]>
  validateForm(form: FormElement): Promise<boolean>
  getConfidence(form: FormElement): number
}

export interface DetectionEnvironment {
  document: Document
  window: Window
  pageContext: PageContext
  cache?: DetectionCache
  options?: DetectionOptions
}

export interface DetectionCache {
  get<T>(key: string): T | null
  set<T>(key: string, value: T, ttl?: number): void
  clear(): void
  invalidate(pattern?: string): void
}

export interface DetectionOptions {
  strategy?: DetectionStrategy
  maxForms?: number
  timeout?: number
  enableCache?: boolean
  minConfidence?: number
  includeHidden?: boolean
  deepScan?: boolean
}

export interface FormDetectionResult {
  forms: FormElement[]
  summary: DetectionSummary
  performance: PerformanceMetrics
  errors: DetectionError[]
  warnings: string[]
}

export interface DetectionSummary {
  totalFormsFound: number
  highConfidenceForms: number
  averageConfidence: number
  detectionStrategiesUsed: DetectionStrategy[]
  frameworksDetected: WebFramework[]
  recommendedForms: FormElement[]
}

export interface DetectionError {
  code: string
  message: string
  context?: any
  recoverable: boolean
}

// 表单监视器接口
export interface FormMonitor {
  startMonitoring(options?: MonitoringOptions): void
  stopMonitoring(): void
  onFormAdded(callback: (form: FormElement) => void): void
  onFormRemoved(callback: (formId: string) => void): void
  onFormChanged(callback: (form: FormElement, changes: FormChange[]) => void): void
}

export interface MonitoringOptions {
  debounceMs?: number
  enableMutationObserver?: boolean
  enableIntersectionObserver?: boolean
  trackVisibilityChanges?: boolean
}

export interface FormChange {
  type: 'field_added' | 'field_removed' | 'field_modified' | 'form_moved'
  fieldId?: string
  oldValue?: any
  newValue?: any
  timestamp: number
}

// 网站流量数据模型
export interface WebsiteTraffic {
  domain: string
  totalVisits: number
  uniqueVisitors: number
  pageViews: number
  bounceRate: number
  averageVisitDuration: number
  pagesPerVisit: number
  lastUpdated: Date
}

export interface TrafficCountry {
  countryCode: string
  countryName: string
  trafficShare: number
  visits: number
}

export interface TrafficKeyword {
  keyword: string
  trafficShare: number
  position: number
  volume?: number
}

export interface SimilarWebData {
  domain: string
  traffic: WebsiteTraffic
  topCountries: TrafficCountry[]
  topKeywords: TrafficKeyword[]
  globalRank?: number
  countryRank?: number
  categoryRank?: number
  category?: string
}

// 页面扫描相关模型
export interface SubmitLink {
  url: string
  text: string
  type: 'submit' | 'form' | 'signup' | 'contact' | 'apply'
  confidence: number
  context?: string
  isInternalLink: boolean
}

export interface PageScanResult {
  currentUrl: string
  submitLinks: SubmitLink[]
  totalLinksFound: number
  newLinksNotInDatabase: SubmitLink[]
  scannedAt: Date
}

// 用户设置 - 扩展表单检测相关设置
export interface UserSettings {
  apiKey?: string
  isPaidUser: boolean
  theme: 'light' | 'dark' | 'system'
  language: string
  autoOpenSidebar: boolean
  aiOptimizationEnabled: boolean
  
  // 表单检测设置
  formDetection?: {
    strategy: DetectionStrategy
    enableAutoDetection: boolean
    enableRealTimeMonitoring: boolean
    minConfidence: number
    maxFormsPerPage: number
    enableCache: boolean
    cacheTtl: number
    enableDeepScan: boolean
    excludePatterns: string[]
  }
}